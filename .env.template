# Environment Configuration Template for Rwanda Gazette Scraper
# Copy this file to .env and fill in the actual values for local development
# For production deployment, these are managed through GCP Secret Manager

# =============================================================================
# Google Cloud Configuration
# =============================================================================

# Google Cloud Project ID (required)
GOOGLE_CLOUD_PROJECT=rwandan-law-bot-440710

# Google Cloud Storage bucket for storing PDFs
GCS_BUCKET=rwandan_laws

# Vertex AI location for batch processing
VERTEX_AI_LOCATION=us-central1

# Gemini API Key for document processing (stored in Secret Manager for production)
GEMINI_API_KEY=your-gemini-api-key-here

# =============================================================================
# Supabase Configuration
# =============================================================================

# Supabase URL (from your Supabase project dashboard)
SUPABASE_URL=https://esjiwdjofswwwmghrlaa.supabase.co

# Supabase Service Role Key (stored in Secret Manager for production)
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# =============================================================================
# Application Configuration
# =============================================================================

# Scraper rate limiting (requests per second)
SCRAPER_RATE_LIMIT=0.5

# Maximum number of retries for failed requests
SCRAPER_MAX_RETRIES=5

# Maximum concurrent download threads
SCRAPER_MAX_THREADS=2

# Start scraping from this year (optional)
SCRAPER_SINCE_YEAR=2004

# Crawler depth: 1=year-level only, 2=month-level traversal
SCRAPER_CRAWLER_DEPTH=2

# =============================================================================
# Cloud Run Specific (for production deployment)
# =============================================================================

# Port for the web server (Cloud Run uses PORT env var)
PORT=8080

# Service name for Cloud Run
SERVICE_NAME=rwanda-gazette-scraper

# Deployment region
REGION=us-central1

# =============================================================================
# Development Configuration (local only)
# =============================================================================

# Output directory for downloaded files (local development)
OUTPUT_DIR=./data

# SQLite database path for state management (local development)
DB_PATH=./scrape_state.db

# Log level for development (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# Secret Manager References (for production)
# =============================================================================
# These are automatically configured in the Cloud Run service
# Do not set these values locally - they are managed by GCP

# SUPABASE_SERVICE_ROLE_KEY -> projects/PROJECT_ID/secrets/supabase-service-key/versions/latest
# GEMINI_API_KEY -> projects/PROJECT_ID/secrets/gemini-api-key/versions/latest