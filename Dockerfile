# Multi-stage build for minimal production image
FROM python:3.12-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry==1.8.0

# Copy dependency files
WORKDIR /app
COPY pyproject.toml poetry.lock ./

# Configure Poetry: no venv (we're in container), install deps
RUN poetry config virtualenvs.create false \
    && poetry install --no-dev --no-interaction --no-ansi

# Production stage
FROM python:3.12-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd --gid 1000 scraper \
    && useradd --uid 1000 --gid scraper --shell /bin/bash --create-home scraper

# Copy Python packages from builder
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application code
WORKDIR /app
COPY --chown=scraper:scraper . .

# Create data directory for outputs
RUN mkdir -p /app/data && chown scraper:scraper /app/data

# Install application
RUN pip install -e .

# Switch to non-root user
USER scraper

# Set default command for Cloud Run (health check server)
ENTRYPOINT ["gazette-scraper"]
CMD ["serve"]

# Labels for internal container registry
LABEL org.opencontainers.image.title="Rwanda Gazette Scraper"
LABEL org.opencontainers.image.description="Internal tool for scraping official gazette PDFs"
LABEL org.opencontainers.image.version="0.2.0"
LABEL org.opencontainers.image.vendor="Internal Use Only"