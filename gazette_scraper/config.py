"""Configuration management."""

from __future__ import annotations

import os
import re
from pathlib import Path

import toml

from .models import ScrapingConfig


def _expand_env_vars(text: str) -> str:
    """Expand environment variables in configuration values."""
    return re.sub(r'\$\{([^}]+)\}', lambda m: os.getenv(m.group(1), ''), text)


def load_config(config_path: Path | None = None) -> ScrapingConfig:
    """Load configuration from file and environment variables."""

    # Default config file locations
    if config_path is None:
        config_candidates = [
            Path("config.toml"),
            Path.home() / ".config" / "gazette-scraper" / "config.toml",
        ]
        config_path = next((p for p in config_candidates if p.exists()), None)

    # Load from file
    config_data: dict[str, object] = {}
    if config_path and config_path.exists():
        with open(config_path) as f:
            raw_config = toml.load(f)

        # Flatten GCS section if it exists
        if "gcs" in raw_config:
            gcs_section = raw_config.pop("gcs")
            if "bucket" in gcs_section:
                raw_config["gcs_bucket"] = gcs_section["bucket"]
            if "project_id" in gcs_section:
                raw_config["gcs_project_id"] = gcs_section["project_id"]
            if "prefix" in gcs_section:
                raw_config["gcs_prefix"] = gcs_section["prefix"]

        # Flatten Supabase section if it exists
        if "supabase" in raw_config:
            supabase_section = raw_config.pop("supabase")
            if "url" in supabase_section:
                raw_config["supabase_url"] = supabase_section["url"]
            if "service_role_key" in supabase_section:
                raw_config["supabase_key"] = _expand_env_vars(supabase_section["service_role_key"])

        config_data = raw_config

    # Override with environment variables
    env_overrides = {
        "rate_limit": os.getenv("GAZETTE_RATE_LIMIT"),
        "max_threads": os.getenv("GAZETTE_MAX_THREADS"),
        "output_dir": os.getenv("GAZETTE_OUTPUT_DIR"),
        "db_path": os.getenv("GAZETTE_DB_PATH"),
        "gcs_bucket": os.getenv("GAZETTE_GCS_BUCKET"),
        "gcs_project_id": os.getenv("GAZETTE_GCS_PROJECT_ID"),
        "gcs_prefix": os.getenv("GAZETTE_GCS_PREFIX"),
        "since_year": os.getenv("GAZETTE_SINCE_YEAR"),
        "crawler_depth": os.getenv("GAZETTE_CRAWLER_DEPTH"),
        "supabase_url": os.getenv("SUPABASE_URL"),
        "supabase_key": os.getenv("SUPABASE_SERVICE_ROLE_KEY"),
    }

    for key, value in env_overrides.items():
        if value is not None:
            if key in ["rate_limit"]:
                config_data[key] = float(value)
            elif key in ["max_threads", "since_year", "crawler_depth"]:
                config_data[key] = int(value)
            elif key in ["output_dir", "db_path"]:
                config_data[key] = Path(value)
            else:
                config_data[key] = value

    return ScrapingConfig(**config_data)  # type: ignore[arg-type]
