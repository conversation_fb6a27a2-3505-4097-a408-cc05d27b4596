"""GraphRAG query service with hybrid search capabilities."""

from __future__ import annotations

import asyncio
import logging
from pathlib import Path
from typing import Any

import pandas as pd
import graphrag.api as graphrag_api
from graphrag.config.load_config import load_config

from .embeddings import VoyageEmbedder

logger = logging.getLogger(__name__)


class GraphRAGQueryService:
    """Unified query service for GraphRAG with hybrid search."""
    
    def __init__(
        self,
        graphrag_project_root: Path,
        voyage_embedder: VoyageEmbedder,
        tenant_id: str = "rwanda_gov"
    ):
        """Initialize the query service.
        
        Args:
            graphrag_project_root: Path to GraphRAG project root
            voyage_embedder: Voyage embedder instance
            tenant_id: Tenant identifier
        """
        self.project_root = Path(graphrag_project_root)
        self.voyage_embedder = voyage_embedder
        self.tenant_id = tenant_id
        
        # Load GraphRAG configuration
        self.config = load_config(self.project_root)
        
        # Load GraphRAG data
        self._load_graphrag_data()
    
    def _load_graphrag_data(self) -> None:
        """Load GraphRAG parquet files."""
        output_dir = self.project_root / "output"
        
        try:
            self.entities = pd.read_parquet(output_dir / "entities.parquet")
            logger.info(f"Loaded {len(self.entities)} entities")
        except FileNotFoundError:
            logger.warning("entities.parquet not found")
            self.entities = pd.DataFrame()
        
        try:
            self.relationships = pd.read_parquet(output_dir / "relationships.parquet")
            logger.info(f"Loaded {len(self.relationships)} relationships")
        except FileNotFoundError:
            logger.warning("relationships.parquet not found")
            self.relationships = pd.DataFrame()
        
        try:
            self.communities = pd.read_parquet(output_dir / "communities.parquet")
            logger.info(f"Loaded {len(self.communities)} communities")
        except FileNotFoundError:
            logger.warning("communities.parquet not found - run GraphRAG indexing first")
            self.communities = pd.DataFrame()
        
        try:
            self.community_reports = pd.read_parquet(output_dir / "community_reports.parquet")
            logger.info(f"Loaded {len(self.community_reports)} community reports")
        except FileNotFoundError:
            logger.warning("community_reports.parquet not found - run GraphRAG indexing first")
            self.community_reports = pd.DataFrame()
    
    async def global_search(
        self,
        query: str,
        community_level: int = 2,
        response_type: str = "Multiple Paragraphs",
        max_tokens: int = 1500
    ) -> dict[str, Any]:
        """Perform GraphRAG global search.
        
        Args:
            query: Search query
            community_level: Community level for search
            response_type: Type of response format
            max_tokens: Maximum tokens in response
            
        Returns:
            Search results with response and context
        """
        if self.community_reports.empty:
            return {
                "response": "GraphRAG community reports not available. Please run indexing first.",
                "context": [],
                "error": "Missing community reports"
            }
        
        try:
            response, context = await graphrag_api.global_search(
                config=self.config,
                entities=self.entities,
                communities=self.communities,
                community_reports=self.community_reports,
                community_level=community_level,
                dynamic_community_selection=False,
                response_type=response_type,
                query=query
            )
            
            return {
                "response": response,
                "context": context,
                "search_type": "global",
                "community_level": community_level
            }
            
        except Exception as e:
            logger.error(f"Error in global search: {e}")
            return {
                "response": f"Error performing global search: {e}",
                "context": [],
                "error": str(e)
            }
    
    async def local_search(
        self,
        query: str,
        language: str | None = None,
        gazette_date_filter: dict[str, Any] | None = None,
        law_type: str | None = None,
        max_results: int = 20,
        include_entity_context: bool = True
    ) -> dict[str, Any]:
        """Perform hybrid local search using GraphRAG entities + Atlas vector search.
        
        Args:
            query: Search query
            language: Language filter
            gazette_date_filter: Date range filter
            law_type: Law type filter
            max_results: Maximum number of results
            include_entity_context: Whether to include entity context
            
        Returns:
            Search results with chunks and entity context
        """
        try:
            # Step 1: Find relevant entities using GraphRAG
            relevant_entities = self._find_relevant_entities(query)
            
            # Step 2: Embed query for vector search
            query_vector = self.voyage_embedder.embed_query(query)
            
            if not query_vector:
                return {
                    "response": "Error embedding query",
                    "context": [],
                    "error": "Query embedding failed"
                }
            
            # Step 3: Perform vector search
            vector_results = self.voyage_embedder.vector_search(
                query_vector=query_vector,
                tenant_id=self.tenant_id,
                language=language,
                gazette_date_filter=gazette_date_filter,
                law_type=law_type,
                limit=max_results
            )
            
            # Step 4: Combine results
            response = self._format_local_search_response(
                query, vector_results, relevant_entities if include_entity_context else []
            )
            
            return {
                "response": response,
                "context": vector_results,
                "entities": relevant_entities,
                "search_type": "local_hybrid",
                "filters": {
                    "language": language,
                    "gazette_date_filter": gazette_date_filter,
                    "law_type": law_type
                }
            }
            
        except Exception as e:
            logger.error(f"Error in local search: {e}")
            return {
                "response": f"Error performing local search: {e}",
                "context": [],
                "error": str(e)
            }
    
    async def drift_search(
        self,
        query: str,
        community_level: int = 2,
        local_search_limit: int = 20
    ) -> dict[str, Any]:
        """Perform GraphRAG DRIFT search (global + local combined).
        
        Args:
            query: Search query
            community_level: Community level for global search
            local_search_limit: Limit for local search results
            
        Returns:
            DRIFT search results
        """
        try:
            # Note: DRIFT search requires GraphRAG's native implementation
            # This is a simplified version that combines global and local results
            
            # Perform global search
            global_result = await self.global_search(
                query, community_level=community_level
            )
            
            # Perform local search
            local_result = await self.local_search(
                query, max_results=local_search_limit
            )
            
            # Combine results
            combined_response = self._format_drift_response(
                query, global_result, local_result
            )
            
            return {
                "response": combined_response,
                "global_context": global_result.get("context", []),
                "local_context": local_result.get("context", []),
                "search_type": "drift_hybrid",
                "community_level": community_level
            }
            
        except Exception as e:
            logger.error(f"Error in DRIFT search: {e}")
            return {
                "response": f"Error performing DRIFT search: {e}",
                "context": [],
                "error": str(e)
            }
    
    def _find_relevant_entities(self, query: str, max_entities: int = 10) -> list[dict[str, Any]]:
        """Find relevant entities using simple text matching.
        
        Args:
            query: Search query
            max_entities: Maximum number of entities to return
            
        Returns:
            List of relevant entities
        """
        if self.entities.empty:
            return []
        
        # Simple text matching - could be enhanced with semantic search
        query_lower = query.lower()
        
        relevant_entities = []
        for _, entity in self.entities.iterrows():
            title_lower = str(entity.get('title', '')).lower()
            description_lower = str(entity.get('description', '')).lower()
            
            if (query_lower in title_lower or 
                query_lower in description_lower or
                any(word in title_lower for word in query_lower.split())):
                
                relevant_entities.append({
                    'id': entity.get('id'),
                    'title': entity.get('title'),
                    'description': entity.get('description'),
                    'type': entity.get('type'),
                    'metadata': entity.get('metadata', {})
                })
        
        return relevant_entities[:max_entities]
    
    def _format_local_search_response(
        self,
        query: str,
        vector_results: list[dict[str, Any]],
        entities: list[dict[str, Any]]
    ) -> str:
        """Format local search response with citations.
        
        Args:
            query: Original query
            vector_results: Vector search results
            entities: Relevant entities
            
        Returns:
            Formatted response string
        """
        if not vector_results:
            return "No relevant documents found for your query."
        
        response_parts = [f"Based on the Rwanda Gazette documents, here's what I found regarding '{query}':\n"]
        
        # Add entity context if available
        if entities:
            response_parts.append("**Relevant Legal Entities:**")
            for entity in entities[:3]:  # Limit to top 3
                response_parts.append(f"- {entity['title']}: {entity['description']}")
            response_parts.append("")
        
        # Add top search results with citations
        response_parts.append("**Relevant Document Excerpts:**")
        for i, result in enumerate(vector_results[:5], 1):  # Limit to top 5
            text = result.get('text', '')[:500]  # Truncate long text
            if len(result.get('text', '')) > 500:
                text += "..."
            
            citation = f"[{i}] {result.get('article_id', 'Unknown')} ({result.get('language', 'unknown')})"
            response_parts.append(f"\n{citation}\n{text}")
        
        return "\n".join(response_parts)
    
    def _format_drift_response(
        self,
        query: str,
        global_result: dict[str, Any],
        local_result: dict[str, Any]
    ) -> str:
        """Format DRIFT search response combining global and local results.
        
        Args:
            query: Original query
            global_result: Global search result
            local_result: Local search result
            
        Returns:
            Formatted DRIFT response
        """
        response_parts = [f"Comprehensive analysis for '{query}':\n"]
        
        # Add global overview
        if global_result.get("response") and "Error" not in global_result["response"]:
            response_parts.append("**High-Level Overview:**")
            response_parts.append(global_result["response"])
            response_parts.append("")
        
        # Add specific evidence
        if local_result.get("response") and "Error" not in local_result["response"]:
            response_parts.append("**Specific Evidence:**")
            response_parts.append(local_result["response"])
        
        return "\n".join(response_parts)
    
    async def build_index(self) -> dict[str, Any]:
        """Build GraphRAG index (communities and reports).
        
        Returns:
            Build result status
        """
        try:
            await graphrag_api.build_index(config=self.config)
            
            # Reload data after building
            self._load_graphrag_data()
            
            return {
                "status": "success",
                "message": "GraphRAG index built successfully"
            }
            
        except Exception as e:
            logger.error(f"Error building GraphRAG index: {e}")
            return {
                "status": "error",
                "message": f"Error building index: {e}"
            }
