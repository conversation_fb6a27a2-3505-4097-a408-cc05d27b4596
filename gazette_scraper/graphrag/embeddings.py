"""Voyage contextual embeddings for gazette chunks."""

from __future__ import annotations

import logging
import os
from typing import Any

import pandas as pd
import voyageai
from pymongo import MongoClient
from pymongo.collection import Collection

from .models import ChunkEmbedding

logger = logging.getLogger(__name__)


class VoyageEmbedder:
    """Handles Voyage contextual embeddings and MongoDB Atlas storage."""
    
    def __init__(
        self,
        voyage_api_key: str | None = None,
        mongodb_uri: str | None = None,
        database_name: str = "rwanda_laws",
        collection_name: str = "gazette_chunks",
        model: str = "voyage-context-3",
        output_dimension: int = 1024,
    ):
        """Initialize the embedder.
        
        Args:
            voyage_api_key: Voyage AI API key (or from env)
            mongodb_uri: MongoDB Atlas connection string (or from env)
            database_name: MongoDB database name
            collection_name: MongoDB collection name
            model: Voyage model to use
            output_dimension: Embedding dimension (256/512/1024/2048)
        """
        self.model = model
        self.output_dimension = output_dimension
        
        # Initialize Voyage client
        api_key = voyage_api_key or os.getenv("VOYAGE_API_KEY")
        if not api_key:
            raise ValueError("VOYAGE_API_KEY is required")
        self.voyage_client = voyageai.Client(api_key=api_key)
        
        # Initialize MongoDB client
        uri = mongodb_uri or os.getenv("MONGODB_URI")
        if not uri:
            raise ValueError("MONGODB_URI is required")
        
        self.mongo_client = MongoClient(uri)
        self.database = self.mongo_client[database_name]
        self.collection: Collection = self.database[collection_name]
        
        # Ensure vector search index exists
        self._ensure_vector_index()
    
    def embed_text_units(
        self, 
        text_units_path: str, 
        tenant_id: str = "rwanda_gov",
        batch_size: int = 100
    ) -> int:
        """Embed text units and store in MongoDB Atlas.
        
        Args:
            text_units_path: Path to text_units.parquet file
            tenant_id: Tenant identifier
            batch_size: Number of documents to process per batch
            
        Returns:
            Number of embeddings created
        """
        logger.info(f"Loading text units from {text_units_path}")
        
        # Load text units
        df = pd.read_parquet(text_units_path)
        logger.info(f"Loaded {len(df)} text units")
        
        # Group by document_id for contextualized embedding
        doc_groups = df.groupby('document_ids')
        
        total_embedded = 0
        
        for doc_id, group in doc_groups:
            try:
                # Sort chunks by chunk_index if available
                if 'chunk_index' in group.columns:
                    group = group.sort_values('chunk_index')
                
                # Prepare chunks for contextualized embedding
                chunks = group['text'].tolist()
                
                # Get embeddings for this document
                embeddings = self._get_contextualized_embeddings([chunks])
                
                if not embeddings or not embeddings[0]:
                    logger.warning(f"No embeddings returned for document {doc_id}")
                    continue
                
                # Create embedding documents
                embedding_docs = []
                for i, (_, row) in enumerate(group.iterrows()):
                    if i >= len(embeddings[0]):
                        logger.warning(f"Missing embedding for chunk {i} in document {doc_id}")
                        continue
                    
                    chunk_embedding = ChunkEmbedding(
                        chunk_id=row['id'],
                        tenant_id=tenant_id,
                        doc_id=str(doc_id[0]) if isinstance(doc_id, tuple) else str(doc_id),
                        text=row['text'],
                        vector=embeddings[0][i],
                        language=row.get('language', 'unknown'),
                        gazette_date=row.get('gazette_date', ''),
                        law_type=row.get('law_type'),
                        article_id=row.get('article_id'),
                        metadata={
                            'article_no': row.get('article_no'),
                            'chunk_index': row.get('chunk_index'),
                            'pages': row.get('pages', []),
                            'n_tokens': row.get('n_tokens', 0),
                        }
                    )
                    
                    embedding_docs.append(chunk_embedding.model_dump())
                
                # Batch insert to MongoDB
                if embedding_docs:
                    self._upsert_embeddings(embedding_docs)
                    total_embedded += len(embedding_docs)
                    logger.debug(f"Embedded {len(embedding_docs)} chunks for document {doc_id}")
                
            except Exception as e:
                logger.error(f"Error processing document {doc_id}: {e}")
                continue
        
        logger.info(f"Successfully embedded {total_embedded} chunks")
        return total_embedded
    
    def _get_contextualized_embeddings(self, documents_chunks: list[list[str]]) -> list[list[list[float]]]:
        """Get contextualized embeddings from Voyage AI.
        
        Args:
            documents_chunks: List of documents, each containing ordered chunks
            
        Returns:
            List of embeddings per document
        """
        try:
            response = self.voyage_client.contextualized_embed(
                inputs=documents_chunks,
                model=self.model,
                input_type="document",
                output_dimension=self.output_dimension
            )
            
            return [result.embeddings for result in response.results]
            
        except Exception as e:
            logger.error(f"Error getting embeddings from Voyage: {e}")
            return []
    
    def embed_query(self, query: str) -> list[float]:
        """Embed a query for vector search.
        
        Args:
            query: Query text
            
        Returns:
            Query embedding vector
        """
        try:
            response = self.voyage_client.contextualized_embed(
                inputs=[[query]],
                model=self.model,
                input_type="query",
                output_dimension=self.output_dimension
            )
            
            return response.results[0].embeddings[0]
            
        except Exception as e:
            logger.error(f"Error embedding query: {e}")
            return []
    
    def vector_search(
        self,
        query_vector: list[float],
        tenant_id: str,
        language: str | None = None,
        gazette_date_filter: dict[str, Any] | None = None,
        law_type: str | None = None,
        limit: int = 20,
        num_candidates: int = 200
    ) -> list[dict[str, Any]]:
        """Perform vector search in MongoDB Atlas.
        
        Args:
            query_vector: Query embedding vector
            tenant_id: Tenant identifier
            language: Language filter (optional)
            gazette_date_filter: Date range filter (optional)
            law_type: Law type filter (optional)
            limit: Number of results to return
            num_candidates: Number of candidates for vector search
            
        Returns:
            List of search results with scores
        """
        # Build match filter
        match_filter = {"tenant_id": tenant_id}
        
        if language:
            match_filter["language"] = language
        
        if gazette_date_filter:
            match_filter["gazette_date"] = gazette_date_filter
        
        if law_type:
            match_filter["law_type"] = law_type
        
        # Build aggregation pipeline
        pipeline = [
            {"$match": match_filter},
            {
                "$vectorSearch": {
                    "index": "vector_index",
                    "path": "vector",
                    "queryVector": query_vector,
                    "numCandidates": num_candidates,
                    "limit": limit
                }
            },
            {
                "$project": {
                    "chunk_id": 1,
                    "text": 1,
                    "language": 1,
                    "gazette_date": 1,
                    "law_type": 1,
                    "article_id": 1,
                    "metadata": 1,
                    "score": {"$meta": "vectorSearchScore"},
                    "_id": 0
                }
            }
        ]
        
        try:
            results = list(self.collection.aggregate(pipeline))
            logger.debug(f"Vector search returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Error performing vector search: {e}")
            return []
    
    def _upsert_embeddings(self, embedding_docs: list[dict[str, Any]]) -> None:
        """Upsert embeddings to MongoDB with conflict resolution."""
        try:
            # Use bulk operations for efficiency
            from pymongo import UpdateOne
            
            operations = []
            for doc in embedding_docs:
                operations.append(
                    UpdateOne(
                        {"chunk_id": doc["chunk_id"]},
                        {"$set": doc},
                        upsert=True
                    )
                )
            
            if operations:
                result = self.collection.bulk_write(operations)
                logger.debug(
                    f"Bulk write: {result.upserted_count} inserted, "
                    f"{result.modified_count} modified"
                )
                
        except Exception as e:
            logger.error(f"Error upserting embeddings: {e}")
            raise
    
    def _ensure_vector_index(self) -> None:
        """Ensure vector search index exists in MongoDB Atlas."""
        try:
            # Check if index exists
            indexes = list(self.collection.list_search_indexes())
            vector_index_exists = any(
                idx.get("name") == "vector_index" for idx in indexes
            )
            
            if not vector_index_exists:
                logger.warning(
                    "Vector search index 'vector_index' not found. "
                    "Please create it in MongoDB Atlas with the following specification:\n"
                    "{\n"
                    '  "fields": [\n'
                    '    {\n'
                    '      "type": "vector",\n'
                    '      "path": "vector",\n'
                    f'      "numDimensions": {self.output_dimension},\n'
                    '      "similarity": "dotProduct"\n'
                    '    }\n'
                    '  ]\n'
                    "}"
                )
            else:
                logger.info("Vector search index found")
                
        except Exception as e:
            logger.warning(f"Could not check vector index: {e}")
    
    def close(self) -> None:
        """Close MongoDB connection."""
        if hasattr(self, 'mongo_client'):
            self.mongo_client.close()
