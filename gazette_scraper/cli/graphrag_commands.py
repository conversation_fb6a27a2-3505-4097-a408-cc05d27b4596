"""CLI commands for GraphRAG operations."""

from __future__ import annotations

import asyncio
import logging
from pathlib import Path

import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

from ..graphrag import B<PERSON><PERSON>GMapper, VoyageEmbedder, GraphRAGQueryService
from ..graphrag.config import create_graphrag_project

console = Console()
logger = logging.getLogger(__name__)


@click.group()
def graphrag() -> None:
    """GraphRAG operations for Rwanda Gazette analysis."""
    pass


@graphrag.command()
@click.option(
    "--input-dir",
    type=click.Path(exists=True, path_type=Path),
    required=True,
    help="Directory containing gazette.json files"
)
@click.option(
    "--output-dir",
    type=click.Path(path_type=Path),
    default="./graphrag_data",
    help="Output directory for BYOG parquet files"
)
@click.option(
    "--tenant-id",
    default="rwanda_gov",
    help="Tenant identifier for multi-tenancy"
)
@click.option(
    "--chunk-size",
    default=1000,
    help="Target chunk size in tokens"
)
@click.option(
    "--chunk-overlap",
    default=100,
    help="Overlap between chunks in tokens"
)
def build_byog(
    input_dir: Path,
    output_dir: Path,
    tenant_id: str,
    chunk_size: int,
    chunk_overlap: int
) -> None:
    """Build GraphRAG BYOG parquet files from gazette JSON data."""
    console.print(f"[bold blue]Building BYOG data from {input_dir}[/bold blue]")
    
    try:
        # Initialize mapper
        mapper = BYOGMapper(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
        
        # Process files
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Processing gazette files...", total=None)
            
            byog_data = mapper.process_gazette_files(
                input_dir=input_dir,
                output_dir=output_dir,
                tenant_id=tenant_id
            )
            
            progress.update(task, description="✓ BYOG data generated")
        
        console.print(f"[green]✓ Generated BYOG data:[/green]")
        console.print(f"  • {len(byog_data.text_units)} text units")
        console.print(f"  • {len(byog_data.entities)} entities")
        console.print(f"  • {len(byog_data.relationships)} relationships")
        console.print(f"  • Output: {output_dir}")
        
    except Exception as e:
        console.print(f"[red]✗ Error building BYOG data: {e}[/red]")
        raise click.ClickException(str(e))


@graphrag.command()
@click.option(
    "--text-units-path",
    type=click.Path(exists=True, path_type=Path),
    required=True,
    help="Path to text_units.parquet file"
)
@click.option(
    "--tenant-id",
    default="rwanda_gov",
    help="Tenant identifier"
)
@click.option(
    "--batch-size",
    default=100,
    help="Batch size for embedding processing"
)
@click.option(
    "--model",
    default="voyage-context-3",
    help="Voyage model to use"
)
@click.option(
    "--dimension",
    default=1024,
    help="Embedding dimension"
)
def embed_chunks(
    text_units_path: Path,
    tenant_id: str,
    batch_size: int,
    model: str,
    dimension: int
) -> None:
    """Generate Voyage contextual embeddings and store in MongoDB Atlas."""
    console.print(f"[bold blue]Generating embeddings for {text_units_path}[/bold blue]")
    
    try:
        # Initialize embedder
        embedder = VoyageEmbedder(
            model=model,
            output_dimension=dimension
        )
        
        # Generate embeddings
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Generating embeddings...", total=None)
            
            total_embedded = embedder.embed_text_units(
                text_units_path=str(text_units_path),
                tenant_id=tenant_id,
                batch_size=batch_size
            )
            
            progress.update(task, description="✓ Embeddings generated")
        
        console.print(f"[green]✓ Generated {total_embedded} embeddings[/green]")
        console.print(f"  • Model: {model}")
        console.print(f"  • Dimension: {dimension}")
        console.print(f"  • Stored in MongoDB Atlas")
        
        # Close embedder
        embedder.close()
        
    except Exception as e:
        console.print(f"[red]✗ Error generating embeddings: {e}[/red]")
        raise click.ClickException(str(e))


@graphrag.command()
@click.option(
    "--project-name",
    default="ailex_rwanda",
    help="GraphRAG project name"
)
@click.option(
    "--byog-dir",
    type=click.Path(exists=True, path_type=Path),
    help="Directory containing BYOG parquet files"
)
@click.option(
    "--enable-vector-store",
    is_flag=True,
    help="Enable LanceDB vector store"
)
def init_project(
    project_name: str,
    byog_dir: Path | None,
    enable_vector_store: bool
) -> None:
    """Initialize GraphRAG project."""
    console.print(f"[bold blue]Initializing GraphRAG project: {project_name}[/bold blue]")
    
    try:
        # Create project
        config = create_graphrag_project(
            project_name=project_name,
            enable_vector_store=enable_vector_store
        )
        
        # Copy BYOG files if provided
        if byog_dir:
            config.copy_byog_files(byog_dir)
            console.print(f"[green]✓ Copied BYOG files from {byog_dir}[/green]")
        
        console.print(f"[green]✓ GraphRAG project initialized[/green]")
        console.print(f"  • Project: {config.project_root}")
        console.print(f"  • Vector store: {'enabled' if enable_vector_store else 'disabled'}")
        
    except Exception as e:
        console.print(f"[red]✗ Error initializing project: {e}[/red]")
        raise click.ClickException(str(e))


@graphrag.command()
@click.option(
    "--project-root",
    type=click.Path(exists=True, path_type=Path),
    default="./ailex_rwanda",
    help="GraphRAG project root directory"
)
def build_index(project_root: Path) -> None:
    """Build GraphRAG index (communities and reports)."""
    console.print(f"[bold blue]Building GraphRAG index for {project_root}[/bold blue]")
    
    try:
        # Initialize query service (which loads config)
        embedder = VoyageEmbedder()
        query_service = GraphRAGQueryService(
            graphrag_project_root=project_root,
            voyage_embedder=embedder
        )
        
        # Build index
        async def build() -> None:
            result = await query_service.build_index()
            return result
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Building GraphRAG index...", total=None)
            
            result = asyncio.run(build())
            
            if result["status"] == "success":
                progress.update(task, description="✓ GraphRAG index built")
                console.print(f"[green]✓ {result['message']}[/green]")
            else:
                progress.update(task, description="✗ Index build failed")
                console.print(f"[red]✗ {result['message']}[/red]")
        
        # Close connections
        embedder.close()
        
    except Exception as e:
        console.print(f"[red]✗ Error building index: {e}[/red]")
        raise click.ClickException(str(e))


@graphrag.command()
@click.option(
    "--project-root",
    type=click.Path(exists=True, path_type=Path),
    default="./ailex_rwanda",
    help="GraphRAG project root directory"
)
@click.option(
    "--query",
    required=True,
    help="Search query"
)
@click.option(
    "--search-type",
    type=click.Choice(["global", "local", "drift"]),
    default="local",
    help="Type of search to perform"
)
@click.option(
    "--language",
    type=click.Choice(["rw", "en", "fr"]),
    help="Language filter for local search"
)
@click.option(
    "--max-results",
    default=10,
    help="Maximum number of results"
)
def query(
    project_root: Path,
    query: str,
    search_type: str,
    language: str | None,
    max_results: int
) -> None:
    """Query the GraphRAG system."""
    console.print(f"[bold blue]Performing {search_type} search: '{query}'[/bold blue]")
    
    try:
        # Initialize query service
        embedder = VoyageEmbedder()
        query_service = GraphRAGQueryService(
            graphrag_project_root=project_root,
            voyage_embedder=embedder
        )
        
        # Perform search
        async def search() -> dict:
            if search_type == "global":
                return await query_service.global_search(query)
            elif search_type == "local":
                return await query_service.local_search(
                    query, language=language, max_results=max_results
                )
            elif search_type == "drift":
                return await query_service.drift_search(query)
            else:
                raise ValueError(f"Unknown search type: {search_type}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Searching...", total=None)
            
            result = asyncio.run(search())
            
            progress.update(task, description="✓ Search completed")
        
        # Display results
        console.print(f"\n[bold green]Search Results ({search_type}):[/bold green]")
        console.print(result.get("response", "No response"))
        
        if "error" in result:
            console.print(f"\n[red]Error: {result['error']}[/red]")
        
        # Close connections
        embedder.close()
        
    except Exception as e:
        console.print(f"[red]✗ Error performing search: {e}[/red]")
        raise click.ClickException(str(e))
