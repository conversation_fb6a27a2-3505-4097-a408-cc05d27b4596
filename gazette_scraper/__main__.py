"""Command-line interface for the gazette scraper."""

from __future__ import annotations

import logging
import sys
from pathlib import Path

import click
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.table import Table

from .config import load_config
from .pipeline import GazettePipeline
from .cli.graphrag_commands import graphrag

console = Console()


def setup_logging(verbose: bool = False, log_level: str = "INFO") -> None:
    """Set up logging configuration."""
    # Parse log level
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
    }
    level = level_map.get(log_level.upper(), logging.INFO)
    if verbose:
        level = logging.DEBUG

    # Console handler with rich formatting
    console_handler = RichHandler(console=console, rich_tracebacks=True)
    console_handler.setLevel(level)

    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # File handler with rotation
    from logging.handlers import RotatingFileHandler

    log_file = log_dir / "gazette_scraper.log"
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10 * 1024 * 1024,
        backupCount=5,  # 10MB files, keep 5
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(
        logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    )

    # Root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

    # Suppress noisy third-party loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("urllib3.connectionpool").setLevel(logging.WARNING)


@click.group()
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
@click.option(
    "--config",
    "-c",
    type=click.Path(exists=True, path_type=Path),
    help="Configuration file path",
)
@click.option(
    "--log-level",
    type=click.Choice(["DEBUG", "INFO", "WARNING", "ERROR"]),
    default="INFO",
    help="Set logging level",
)
@click.pass_context
def cli(ctx: click.Context, verbose: bool, config: Path | None, log_level: str) -> None:
    """Rwanda Minijust Official Gazette Scraper."""
    setup_logging(verbose, log_level)

    ctx.ensure_object(dict)
    ctx.obj["config"] = load_config(config)
    ctx.obj["verbose"] = verbose


# Add GraphRAG commands
cli.add_command(graphrag)


@cli.command()
@click.option(
    "--out",
    "-o",
    type=click.Path(path_type=Path),
    help="Output directory (overrides config)",
)
@click.option("--since", type=int, help="Only download from this year onwards")
@click.option("--threads", "-t", type=int, help="Number of download threads")
@click.option("--proxy", multiple=True, help="Proxy URLs (can specify multiple)")
@click.option("--rate", type=float, help="Rate limit (requests per second)")
@click.option("--max-retries", type=int, help="Maximum number of retries")
@click.option("--dry-run", is_flag=True, help="Discover files but do not download")
@click.option(
    "--force",
    "-f",
    is_flag=True,
    help="Re-download existing files (not applicable to dry-run)",
)
@click.pass_context
def fetch(
    ctx: click.Context,
    out: Path | None,
    since: int | None,
    threads: int | None,
    proxy: tuple[str, ...],
    rate: float | None,
    max_retries: int | None,
    dry_run: bool,
    force: bool,  # noqa: ARG001
) -> None:
    """Fetch Official Gazette PDFs from minijust.gov.rw."""
    config = ctx.obj["config"]

    # Note: force parameter reserved for future implementation

    # Override config with command line options
    if out:
        config.output_dir = out
    if since:
        config.since_year = since
    if threads:
        config.max_threads = threads
    if proxy:
        config.proxies = list(proxy)
    if rate:
        config.rate_limit = rate
    if max_retries:
        config.max_retries = max_retries

    config.dry_run = dry_run

    # Show configuration
    console.print(
        f"[bold green]Starting gazette scraper ({'DRY RUN' if dry_run else 'LIVE'})...[/bold green]"
    )
    console.print(f"Output directory: {config.output_dir}")
    console.print(f"Since year: {config.since_year or 'all years'}")
    console.print(f"Threads: {config.max_threads}")
    console.print(f"Rate limit: {config.rate_limit} req/sec")
    console.print(f"Max retries: {config.max_retries}")

    if config.proxies:
        console.print(f"Proxies: {config.proxies}")

    try:
        pipeline = GazettePipeline(config)
        result = pipeline.run(dry_run=dry_run)

        # Show summary
        if dry_run:
            console.print("[bold blue]Dry run completed![/bold blue]")
            console.print(f"Total files discovered: {result.total_discovered}")
            console.print(f"Manifest written to: {config.output_dir}/gazettes.csv")

            # Show counts by year and month
            if result.total_discovered > 0:
                _show_discovery_summary(config)
        else:
            if result.errors > 0:
                console.print(f"[red]Completed with {result.errors} errors[/red]")
                sys.exit(1)
            else:
                console.print("[green]Successfully completed![/green]")
                console.print(
                    f"Downloaded: {result.downloaded}, Skipped: {result.skipped}"
                )

    except KeyboardInterrupt:
        console.print("[yellow]Interrupted by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Fatal error: {e}[/red]")
        if ctx.obj["verbose"]:
            console.print_exception()
        sys.exit(1)


def _show_discovery_summary(config) -> None:  # type: ignore[no-untyped-def]
    """Show discovery summary from the state database."""
    from .state import ScrapingState

    state = ScrapingState(config.db_path)
    stats = state.get_stats()

    # Create table for year summary
    year_table = Table(title="Discovery Summary by Year")
    year_table.add_column("Year", style="cyan")
    year_table.add_column("Files Discovered", justify="right", style="green")

    for year, count in sorted(stats["discovered_by_year"].items(), reverse=True):
        year_table.add_row(str(year), str(count))

    console.print(year_table)

    # Create table for recent months
    if stats["discovered_by_month"]:
        month_table = Table(title="Discovery Summary by Month (Recent)")
        month_table.add_column("Month", style="cyan")
        month_table.add_column("Files Discovered", justify="right", style="green")

        # Show last 12 months
        recent_months = list(stats["discovered_by_month"].items())[:12]
        for month_key, count in recent_months:
            month_table.add_row(month_key, str(count))

        console.print(month_table)


@cli.command()
@click.pass_context
def stats(ctx: click.Context) -> None:
    """Show comprehensive scraping statistics."""
    config = ctx.obj["config"]

    from .state import ScrapingState

    state = ScrapingState(config.db_path)
    stats = state.get_stats()

    # Overview table
    overview_table = Table(title="Scraping Overview")
    overview_table.add_column("Metric", style="cyan")
    overview_table.add_column("Count", justify="right", style="green")

    overview_table.add_row("Total Discovered", str(stats["total_discovered"]))
    overview_table.add_row("Total Downloaded", str(stats["total_downloaded"]))
    overview_table.add_row("Total Failed", str(stats["total_failed"]))
    overview_table.add_row("Discovered (Last 24h)", str(stats["discovered_last_24h"]))
    overview_table.add_row("Downloaded (Last 24h)", str(stats["downloaded_last_24h"]))

    console.print(overview_table)

    # Downloads by year
    if stats["downloaded_by_year"]:
        year_table = Table(title="Downloads by Year")
        year_table.add_column("Year", style="cyan")
        year_table.add_column("Downloaded", justify="right", style="green")
        year_table.add_column("Discovered", justify="right", style="yellow")

        for year in sorted(stats["downloaded_by_year"].keys(), reverse=True):
            downloaded = stats["downloaded_by_year"].get(year, 0)
            discovered = stats["discovered_by_year"].get(year, 0)
            year_table.add_row(str(year), str(downloaded), str(discovered))

        console.print(year_table)

    # Recent months
    if stats["downloaded_by_month"]:
        month_table = Table(title="Recent Activity by Month")
        month_table.add_column("Month", style="cyan")
        month_table.add_column("Downloaded", justify="right", style="green")
        month_table.add_column("Discovered", justify="right", style="yellow")

        # Show last 12 months
        recent_months = list(stats["downloaded_by_month"].keys())[:12]
        for month_key in recent_months:
            downloaded = stats["downloaded_by_month"].get(month_key, 0)
            discovered = stats["discovered_by_month"].get(month_key, 0)
            month_table.add_row(month_key, str(downloaded), str(discovered))

        console.print(month_table)

    # Error summary
    if stats["errors_by_status"]:
        error_table = Table(title="Errors by HTTP Status")
        error_table.add_column("Status Code", style="red")
        error_table.add_column("Count", justify="right", style="red")

        for status, count in sorted(stats["errors_by_status"].items()):
            error_table.add_row(str(status), str(count))

        console.print(error_table)


@cli.command()
@click.option(
    "--limit", "-l", type=int, default=50, help="Maximum number of files to show"
)
@click.option("--year", type=int, help="Filter by year")
@click.pass_context
def list_files(ctx: click.Context, limit: int, year: int | None) -> None:
    """List downloaded files."""
    config = ctx.obj["config"]

    from .state import ScrapingState

    state = ScrapingState(config.db_path)
    files = state.get_downloaded_files()

    if not files:
        console.print("No files downloaded yet.")
        return

    # Filter by year if specified
    if year:
        files = [f for f in files if f["year"] == year]
        if not files:
            console.print(f"No files downloaded for year {year}.")
            return

    # Create table
    files_table = Table(
        title=f"Downloaded Files ({len(files)} total, showing {min(limit, len(files))})"
    )
    files_table.add_column("Year", style="cyan")
    files_table.add_column("Month", style="cyan")
    files_table.add_column("Filename", style="green")
    files_table.add_column("Size", justify="right", style="yellow")
    files_table.add_column("Downloaded", style="dim")

    for file in files[:limit]:
        files_table.add_row(
            str(file["year"]),
            f"{file['month']:02d}",
            file["filename"][:50] + ("..." if len(file["filename"]) > 50 else ""),
            file["size_str"] or "",
            file["downloaded_at"][:10] if file["downloaded_at"] else "",
        )

    console.print(files_table)

    if len(files) > limit:
        console.print(
            f"[dim]... and {len(files) - limit} more files (use --limit to show more)[/dim]"
        )


@cli.command()
@click.option(
    "--pdf",
    type=click.Path(exists=True, path_type=Path),
    required=True,
    help="Input PDF file to extract"
)
@click.option(
    "--out",
    type=click.Path(path_type=Path),
    default="./out",
    help="Output directory for extracted content"
)
@click.option(
    "--batch-pages",
    type=int,
    default=4,
    help="Number of pages to send per Gemini API call"
)
@click.option(
    "--api-key",
    type=str,
    help="Google API key (overrides GOOGLE_API_KEY env var)"
)
@click.option(
    "--model",
    type=str,
    default="gemini-2.0-flash-exp",
    help="Gemini model to use"
)
@click.pass_context
def extract(
    ctx: click.Context,
    pdf: Path,
    out: Path,
    batch_pages: int,
    api_key: str | None,
    model: str
) -> None:
    """Extract structured content from a tri-lingual Rwandan Gazette PDF.

    This tool uses Gemini AI to extract articles, tables, and metadata from
    Official Gazette PDFs, producing JSON, CSV, and HTML outputs suitable
    for GraphRAG ingestion.
    """
    from .extractor.app import extract_gazette

    # Call the extractor with the same parameters
    try:
        extract_gazette.callback(
            pdf=pdf,
            out=out,
            batch_pages=batch_pages,
            api_key=api_key,
            model=model,
            verbose=ctx.obj["verbose"]
        )
    except Exception as e:
        console.print(f"[red]Extraction failed: {e}[/red]")
        if ctx.obj["verbose"]:
            console.print_exception()
        sys.exit(1)


@cli.command()
@click.pass_context
def sync_supabase(ctx: click.Context) -> None:
    """Sync unsynced files from SQLite to Supabase."""
    config = ctx.obj["config"]

    console.print("[bold yellow]Starting Supabase sync...[/bold yellow]")

    # Check if Supabase is configured
    if not config.supabase_url or not config.supabase_key:
        console.print("[red]Error: Supabase is not configured. Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.[/red]")
        sys.exit(1)

    from .supabase_client import sync_unsynced_files

    try:
        stats = sync_unsynced_files(str(config.db_path))

        # Create summary table
        summary_table = Table(title="Supabase Sync Summary")
        summary_table.add_column("Status", style="cyan")
        summary_table.add_column("Count", justify="right", style="green")

        summary_table.add_row("Successfully synced", str(stats["synced"]))
        summary_table.add_row("Failed to sync", str(stats["failed"]))
        summary_table.add_row("Skipped", str(stats["skipped"]))

        console.print(summary_table)

        if stats["synced"] > 0:
            console.print(f"[green]✓ {stats['synced']} files successfully synced to Supabase[/green]")

        if stats["failed"] > 0:
            console.print(f"[red]✗ {stats['failed']} files failed to sync[/red]")
            sys.exit(1)

        if stats["synced"] == 0 and stats["failed"] == 0 and stats["skipped"] == 0:
            console.print("[yellow]No files to sync (all already synced)[/yellow]")

    except Exception as e:
        console.print(f"[red]Sync failed: {e}[/red]")
        if ctx.obj["verbose"]:
            console.print_exception()
        sys.exit(1)


@cli.command()
@click.option("--port", type=int, default=8080, help="Port to run the health check server on")
@click.pass_context
def serve(ctx: click.Context, port: int) -> None:
    """Start the health check server for Cloud Run deployment."""
    from .health_server import start_health_server
    
    console.print(f"[bold green]Starting health check server on port {port}...[/bold green]")
    
    try:
        server = start_health_server(port)
        console.print(f"[green]✓ Server running at http://localhost:{port}[/green]")
        console.print("Available endpoints:")
        console.print("  /health - Liveness probe")
        console.print("  /ready - Readiness probe") 
        console.print("  / - Service information")
        console.print("\nPress Ctrl+C to stop...")
        
        # Keep the main thread alive
        while server.running:
            import time
            time.sleep(1)
            
    except KeyboardInterrupt:
        console.print("\n[yellow]Shutting down server...[/yellow]")
        server.stop()
        console.print("[green]Server stopped[/green]")
    except Exception as e:
        console.print(f"[red]Server failed: {e}[/red]")
        if ctx.obj["verbose"]:
            console.print_exception()
        sys.exit(1)


@cli.command()
@click.option("--view", type=click.Choice([
    "overview", "types", "categories", "keywords", "timeline", "errors"
]), default="overview", help="Choose analytics view to display")
@click.pass_context
def analytics(ctx: click.Context, view: str) -> None:
    """Show document analytics and classification statistics."""
    config = ctx.obj["config"]
    
    # Check if Supabase is configured
    if not config.supabase_url or not config.supabase_key:
        console.print("[red]Error: Supabase is not configured. Analytics require Supabase connection.[/red]")
        console.print("[yellow]Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.[/yellow]")
        sys.exit(1)
    
    from .supabase_client import get_supabase_client
    
    client = get_supabase_client()
    if not client:
        console.print("[red]Failed to connect to Supabase.[/red]")
        sys.exit(1)
    
    try:
        if view == "overview":
            _show_coverage_analysis(client)
            _show_recent_activity(client)
        elif view == "types":
            _show_document_type_stats(client)
        elif view == "categories":
            _show_subject_category_stats(client)
        elif view == "keywords":
            _show_keyword_analysis(client)
        elif view == "timeline":
            _show_document_timeline(client)
        elif view == "errors":
            _show_error_analysis(client)
            
    except Exception as e:
        console.print(f"[red]Analytics failed: {e}[/red]")
        if ctx.obj["verbose"]:
            console.print_exception()
        sys.exit(1)


def _show_coverage_analysis(client) -> None:  # type: ignore[no-untyped-def]
    """Show high-level coverage statistics."""
    try:
        response = client.table("coverage_analysis").select("*").execute()  # type: ignore[attr-defined]
        
        if not response.data:
            console.print("[yellow]No coverage data available.[/yellow]")
            return
            
        coverage_table = Table(title="📊 Legal Document Collection Overview")
        coverage_table.add_column("Metric", style="cyan")
        coverage_table.add_column("Value", justify="right", style="green")
        
        for row in response.data:
            coverage_table.add_row(row["metric"], row["value"])
            
        console.print(coverage_table)
        
    except Exception as e:
        console.print(f"[red]Failed to load coverage analysis: {e}[/red]")


def _show_recent_activity(client) -> None:  # type: ignore[no-untyped-def]
    """Show recent activity dashboard."""
    try:
        response = client.table("recent_activity").select("*").execute()  # type: ignore[attr-defined]
        
        if not response.data:
            console.print("[yellow]No recent activity data available.[/yellow]")
            return
            
        activity_table = Table(title="📈 Recent Scraping Activity")
        activity_table.add_column("Period", style="cyan")
        activity_table.add_column("Documents Added", justify="right", style="green")
        activity_table.add_column("Document Types", justify="right", style="yellow")
        activity_table.add_column("Total Size (MB)", justify="right", style="blue")
        
        for row in response.data:
            size_mb = round(row["total_bytes"] / (1024*1024), 1) if row["total_bytes"] else 0
            activity_table.add_row(
                row["period"], 
                str(row["documents_added"]), 
                str(row["document_types"]),
                str(size_mb)
            )
            
        console.print(activity_table)
        
    except Exception as e:
        console.print(f"[red]Failed to load recent activity: {e}[/red]")


def _show_document_type_stats(client) -> None:  # type: ignore[no-untyped-def]
    """Show document type distribution."""
    try:
        response = client.table("document_type_stats").select("*").execute()  # type: ignore[attr-defined]
        
        if not response.data:
            console.print("[yellow]No document type data available.[/yellow]")
            return
            
        types_table = Table(title="📄 Document Type Distribution")
        types_table.add_column("Document Type", style="cyan")
        types_table.add_column("Count", justify="right", style="green")
        types_table.add_column("Percentage", justify="right", style="yellow")
        types_table.add_column("Size (MB)", justify="right", style="blue")
        types_table.add_column("Years", style="dim")
        
        for row in response.data:
            size_mb = round(row["total_size_bytes"] / (1024*1024), 1) if row["total_size_bytes"] else 0
            year_range = f"{row['earliest_year']}-{row['latest_year']}" if row['earliest_year'] != row['latest_year'] else str(row['earliest_year'])
            types_table.add_row(
                row["document_type"].replace("_", " ").title(),
                str(row["file_count"]),
                f"{row['percentage']}%",
                str(size_mb),
                year_range
            )
            
        console.print(types_table)
        
    except Exception as e:
        console.print(f"[red]Failed to load document type stats: {e}[/red]")


def _show_subject_category_stats(client) -> None:  # type: ignore[no-untyped-def]
    """Show subject category distribution."""
    try:
        response = client.table("subject_category_stats").select("*").execute()  # type: ignore[attr-defined]
        
        if not response.data:
            console.print("[yellow]No subject category data available.[/yellow]")
            return
            
        categories_table = Table(title="🏷️ Subject Category Distribution")
        categories_table.add_column("Subject Category", style="cyan")
        categories_table.add_column("Count", justify="right", style="green")
        categories_table.add_column("Percentage", justify="right", style="yellow")
        categories_table.add_column("Avg Size (KB)", justify="right", style="blue")
        categories_table.add_column("Years", style="dim")
        
        for row in response.data:
            avg_size_kb = round(row["avg_size_bytes"] / 1024, 1) if row["avg_size_bytes"] else 0
            year_range = f"{row['earliest_year']}-{row['latest_year']}" if row['earliest_year'] != row['latest_year'] else str(row['earliest_year'])
            categories_table.add_row(
                row["subject_category"].replace("_", " ").title(),
                str(row["file_count"]),
                f"{row['percentage']}%",
                str(avg_size_kb),
                year_range
            )
            
        console.print(categories_table)
        
    except Exception as e:
        console.print(f"[red]Failed to load subject category stats: {e}[/red]")


def _show_keyword_analysis(client) -> None:  # type: ignore[no-untyped-def]
    """Show keyword frequency analysis."""
    try:
        response = client.table("keyword_analysis").select("*").limit(20).execute()  # type: ignore[attr-defined]
        
        if not response.data:
            console.print("[yellow]No keyword data available.[/yellow]")
            return
            
        keywords_table = Table(title="🔍 Top 20 Keywords")
        keywords_table.add_column("Keyword", style="cyan")
        keywords_table.add_column("Frequency", justify="right", style="green")
        keywords_table.add_column("Document Types", style="yellow")
        keywords_table.add_column("Years Active", style="dim")
        
        for row in response.data:
            doc_types = ", ".join(row["document_types"][:3]) if row["document_types"] else ""
            if len(row["document_types"]) > 3:
                doc_types += "..."
            year_range = f"{row['first_appearance']}-{row['last_appearance']}" if row['first_appearance'] != row['last_appearance'] else str(row['first_appearance'])
            keywords_table.add_row(
                row["keyword"],
                str(row["frequency"]),
                doc_types,
                year_range
            )
            
        console.print(keywords_table)
        
    except Exception as e:
        console.print(f"[red]Failed to load keyword analysis: {e}[/red]")


def _show_document_timeline(client) -> None:  # type: ignore[no-untyped-def]
    """Show document production timeline."""
    try:
        response = client.table("document_timeline").select("*").limit(24).execute()  # type: ignore[attr-defined]
        
        if not response.data:
            console.print("[yellow]No timeline data available.[/yellow]")
            return
            
        timeline_table = Table(title="📅 Document Production Timeline (Last 24 Months)")
        timeline_table.add_column("Year-Month", style="cyan")
        timeline_table.add_column("Total Docs", justify="right", style="green")
        timeline_table.add_column("Types", justify="right", style="yellow")
        timeline_table.add_column("Names", justify="right", style="blue")
        timeline_table.add_column("Land", justify="right", style="magenta")
        timeline_table.add_column("Coop", justify="right", style="red")
        
        for row in response.data:
            month_key = f"{row['year']}-{row['month']:02d}"
            timeline_table.add_row(
                month_key,
                str(row["total_documents"]),
                str(row["unique_document_types"]),
                str(row["names_docs"] or 0),
                str(row["land_docs"] or 0),
                str(row["cooperative_docs"] or 0)
            )
            
        console.print(timeline_table)
        
    except Exception as e:
        console.print(f"[red]Failed to load document timeline: {e}[/red]")


def _show_error_analysis(client) -> None:  # type: ignore[no-untyped-def]
    """Show error analysis."""
    try:
        response = client.table("error_analysis").select("*").limit(15).execute()  # type: ignore[attr-defined]
        
        if not response.data:
            console.print("[green]No errors recorded! 🎉[/green]")
            return
            
        errors_table = Table(title="⚠️ Download Error Analysis")
        errors_table.add_column("Error Message", style="red")
        errors_table.add_column("HTTP Status", justify="center", style="yellow")
        errors_table.add_column("Count", justify="right", style="green")
        errors_table.add_column("Pages Affected", justify="right", style="blue")
        errors_table.add_column("Last Seen", style="dim")
        
        for row in response.data:
            error_msg = row["error_message"][:50] + ("..." if len(row["error_message"]) > 50 else "")
            last_seen = row["last_occurrence"][:10] if row["last_occurrence"] else ""
            errors_table.add_row(
                error_msg,
                str(row["http_status"]) if row["http_status"] else "N/A",
                str(row["error_count"]),
                str(row["affected_pages"]),
                last_seen
            )
            
        console.print(errors_table)
        
    except Exception as e:
        console.print(f"[red]Failed to load error analysis: {e}[/red]")


def main() -> None:
    """Entry point for the CLI."""
    cli()


if __name__ == "__main__":
    main()
