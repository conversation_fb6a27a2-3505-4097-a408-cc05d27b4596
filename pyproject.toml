[tool.poetry]
name = "gazette-scraper"
version = "0.2.0"
description = "Rwanda Minijust Official Gazette Scraper"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "gazette_scraper"}]

[tool.poetry.dependencies]
python = "^3.11"
requests = "^2.31.0"
beautifulsoup4 = "^4.12.2"
tqdm = "^4.66.1"
tenacity = "^8.2.3"
rich = "^13.7.0"
pydantic = "^2.5.0"
click = "^8.1.7"
toml = "^0.10.2"
google-cloud-storage = {version = "^2.10.0", optional = true}
google-cloud-aiplatform = {version = "^1.38.0", optional = true}
supabase = "^2.0.0"
google-generativeai = "^0.8.0"
pillow = "^10.0.0"
lxml = "^4.9.0"
rapidfuzz = "^3.5.0"
python-dotenv = "^1.0.0"
pymupdf = "^1.23.0"
json-repair = "^0.25.0"

[tool.poetry.extras]
gcs = ["google-cloud-storage"]
batch = ["google-cloud-aiplatform", "google-cloud-storage"]

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-cov = "^4.1.0"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
ruff = "^0.12.7"

[tool.poetry.scripts]
gazette-scraper = "gazette_scraper.__main__:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --cov=gazette_scraper --cov-report=term-missing --cov-fail-under=85"
testpaths = ["tests"]

[tool.ruff]
target-version = "py311"
line-length = 88

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "ARG001", # unused-function-argument
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
]
ignore = [
    "E501",  # line too long, handled by formatter
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
    "SIM117", # combine multiple isinstance checks
]

[tool.ruff.lint.isort]
known-first-party = ["gazette_scraper"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"